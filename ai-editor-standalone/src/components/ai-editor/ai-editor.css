/* AI Editor 样式 */

/* ProseMirror 基础样式 */
.ProseMirror {
  line-height: 1.75;
  outline: none;
}

.ProseMirror .is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

.ProseMirror:not(.dragging) .ProseMirror-selectednode {
  outline: none !important;
  background-color: rgba(59, 130, 246, 0.1);
  transition: background-color 0.2s;
  box-shadow: none;
}

/* 编辑器基础样式 */
.ai-editor-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.dark .ai-editor-container {
  background: #1f2937;
}

/* 编辑器内容区域 */
.ai-editor-container .ProseMirror {
  outline: none;
  min-height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

/* 编辑器容器滚动 */
.ai-editor-container .novel-editor-content {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 编辑器滚动条样式 */
.ai-editor-container .novel-editor-content::-webkit-scrollbar,
.ai-editor-container .ProseMirror::-webkit-scrollbar {
  width: 8px;
}

.ai-editor-container .novel-editor-content::-webkit-scrollbar-track,
.ai-editor-container .ProseMirror::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.ai-editor-container .novel-editor-content::-webkit-scrollbar-thumb,
.ai-editor-container .ProseMirror::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.ai-editor-container .novel-editor-content::-webkit-scrollbar-thumb:hover,
.ai-editor-container .ProseMirror::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.dark .ai-editor-container .novel-editor-content::-webkit-scrollbar-track,
.dark .ai-editor-container .ProseMirror::-webkit-scrollbar-track {
  background: #2d3748;
}

.dark .ai-editor-container .novel-editor-content::-webkit-scrollbar-thumb,
.dark .ai-editor-container .ProseMirror::-webkit-scrollbar-thumb {
  background: #4a5568;
}

.dark .ai-editor-container .novel-editor-content::-webkit-scrollbar-thumb:hover,
.dark .ai-editor-container .ProseMirror::-webkit-scrollbar-thumb:hover {
  background: #718096;
}

/* Markdown输出区域滚动条 */
.ai-editor-container pre::-webkit-scrollbar {
  width: 8px;
}

.ai-editor-container pre::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.ai-editor-container pre::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.ai-editor-container pre::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.dark .ai-editor-container pre::-webkit-scrollbar-track {
  background: #2d3748;
}

.dark .ai-editor-container pre::-webkit-scrollbar-thumb {
  background: #4a5568;
}

.dark .ai-editor-container pre::-webkit-scrollbar-thumb:hover {
  background: #718096;
}

/* 预览区域样式 */
.ai-editor-preview {
  height: 100%;
  overflow-y: auto;
}

.ai-editor-preview .prose {
  max-width: none;
}

/* 工具栏样式 */
.ai-editor-toolbar {
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.dark .ai-editor-toolbar {
  border-bottom-color: #374151;
  background: #1f2937;
}

/* 任务列表样式 */
.ai-editor-task-list {
  list-style: none;
  padding-left: 8px;
}

.ai-editor-task-item {
  display: flex;
  gap: 8px;
  align-items: flex-start;
  margin: 16px 0;
}

/* 图片样式 */
.ai-editor-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.ai-editor-image-uploading {
  opacity: 0.4;
  border: 1px solid #d1d5db;
}

/* 列表样式 */
.ai-editor-bullet-list {
  list-style-type: disc;
  padding-left: 24px;
}

.ai-editor-ordered-list {
  list-style-type: decimal;
  padding-left: 24px;
  margin-top: -8px;
}

.ai-editor-blockquote {
  border-left: 4px solid #3b82f6;
  padding-left: 16px;
  margin: 16px 0;
  font-style: italic;
}

.ai-editor-code {
  background: #f3f4f6;
  padding: 2px 4px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.ai-editor-heading {
  font-weight: 600;
  margin-top: 24px;
  margin-bottom: 16px;
}

/* 链接样式 */
.ai-editor-link {
  color: #3b82f6;
  text-decoration: underline;
  text-underline-offset: 3px;
  transition: color 0.15s;
}

.ai-editor-link:hover {
  color: #1d4ed8;
}

/* 工具栏样式 */
.ai-toolbar-bubble {
  z-index: 50;
}

/* 拖拽手柄样式 */
.drag-handle {
  position: absolute;
  left: -2rem;
  top: 0;
  width: 1.25rem;
  height: 1.25rem;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 0.25rem;
  cursor: grab;
  opacity: 0;
  transition: opacity 0.2s;
}

.drag-handle:hover {
  background: #e5e7eb;
}

.ProseMirror-focused .drag-handle {
  opacity: 1;
}

/* 深色模式支持 */
.dark .ai-editor-blockquote {
  border-left-color: #374151;
  color: #9ca3af;
}

.dark .ai-editor-code {
  background-color: #374151;
  color: #f9fafb;
}

.dark .ai-editor-image {
  border-color: #374151;
}

.dark .ai-editor-image-uploading {
  border-color: #374151;
}

.dark .drag-handle {
  background: #374151;
  border-color: #4b5563;
}

.dark .drag-handle:hover {
  background: #4b5563;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-editor-container .ProseMirror {
    padding: 0.75rem;
  }
}

/* 动画效果 */
.ai-editor-container * {
  transition: all 0.2s ease-in-out;
}

/* 滚动条样式 */
.ai-editor-container .ProseMirror::-webkit-scrollbar {
  width: 8px;
}

.ai-editor-container .ProseMirror::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.ai-editor-container .ProseMirror::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.ai-editor-container .ProseMirror::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
