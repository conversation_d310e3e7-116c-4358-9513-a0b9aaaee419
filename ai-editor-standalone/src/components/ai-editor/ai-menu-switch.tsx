"use client"

import { Fragment, type <PERSON>actN<PERSON>, useEffect } from "react"
import { EditorBubble, useEditor } from "novel"
import { But<PERSON> } from "@/components/ui/button"
import { Sparkles, Loader2 } from "lucide-react"
import { AISelector } from "./ai-selector"

interface AIMenuSwitchProps {
  children?: ReactNode
  open: boolean
  onOpenChange: (open: boolean) => void
  isLoading?: boolean
}

export function AIMenuSwitch({
  children,
  open,
  onOpenChange,
  isLoading = false,
}: AIMenuSwitchProps) {
  const { editor } = useEditor()

  if (!editor) return null

  return (
    <EditorBubble
      tippyOptions={{
        placement: open ? "bottom-start" : "top",
        onHidden: () => {
          onOpenChange(false)
        },
      }}
      className="flex w-fit max-w-[90vw] overflow-hidden rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-xl"
    >
      {open && <AISelector editor={editor} open={open} onOpenChange={onOpenChange} />}
      {!open && (
        <Fragment>
          <Button
            className="gap-1 rounded-none text-purple-500 hover:text-purple-600 hover:bg-purple-50 dark:hover:bg-purple-900/20"
            variant="ghost"
            onClick={() => onOpenChange(true)}
            size="sm"
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Sparkles className="h-4 w-4" />
            )}
            Ask AI
          </Button>
          {children}
        </Fragment>
      )}
    </EditorBubble>
  )
}
